/* Critical CSS for above-the-fold content */
/* This should be inlined in the head for fastest rendering */

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Critical layout styles */
.min-h-screen {
  min-height: 100vh;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-white {
  --tw-gradient-from: #ffffff;
  --tw-gradient-to: rgb(255 255 255 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-teal-50\/30 {
  --tw-gradient-to: rgb(240 253 250 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(240 253 250 / 0.3), var(--tw-gradient-to);
}

.to-white {
  --tw-gradient-to: #ffffff;
}

/* Dark mode critical styles */
@media (prefers-color-scheme: dark) {
  .dark\:from-gray-900 {
    --tw-gradient-from: #111827;
    --tw-gradient-to: rgb(17 24 39 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .dark\:via-teal-900\/30 {
    --tw-gradient-to: rgb(19 78 74 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), rgb(19 78 74 / 0.3), var(--tw-gradient-to);
  }

  .dark\:to-gray-900 {
    --tw-gradient-to: #111827;
  }
}

/* Typography critical styles */
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.font-bold {
  font-weight: 700;
}

.text-white {
  color: rgb(255 255 255);
}

.text-gray-300 {
  color: rgb(209 213 219);
}

.text-teal-600 {
  color: rgb(13 148 136);
}

/* Button critical styles */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-teal-600 {
  --tw-gradient-from: #0d9488;
  --tw-gradient-to: rgb(13 148 136 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-purple-500 {
  --tw-gradient-to: rgb(168 85 247 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247), var(--tw-gradient-to);
}

.to-indigo-600 {
  --tw-gradient-to: #4f46e5;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

/* Animation critical styles */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Loading states */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Skeleton loading */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@media (prefers-color-scheme: dark) {
  .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }
}
