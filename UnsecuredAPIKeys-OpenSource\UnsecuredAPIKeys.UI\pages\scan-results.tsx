import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import DefaultLayout from "@/layouts/default";
import SimpleScanResultsDisplay from "@/components/SimpleScanResultsDisplay";
import { fetchWithRateLimit } from "@/utils/api";
import { ScanResult } from "@/types";
import { Card, CardBody } from "@heroui/card";
import { Button } from "@heroui/button";
import { Spinner } from "@heroui/spinner";
import ExclamationTriangleIcon from '@heroicons/react/24/outline/ExclamationTriangleIcon';
import ArrowLeftIcon from '@heroicons/react/24/outline/ArrowLeftIcon';

export default function ScanResultsPage() {
  const router = useRouter();
  const { scanId, repository } = router.query;
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!scanId) return;

    let isMounted = true;

    const fetchScanResult = async () => {
      try {
        setLoading(true);
        setError(null);

        // Use a unique requestId to prevent cancellation conflicts
        const requestId = `scan-result-${scanId}-${Date.now()}`;
        const response = await fetchWithRateLimit<ScanResult>(`/API/ScanResult/${scanId}`, {
          requestId
        });

        // Check if component is still mounted before updating state
        if (!isMounted) return;

        if (response.cancelled) {
          // Request was cancelled, don't show error
          return;
        }

        if (response.error) {
          throw new Error(response.error);
        }

        if (response.data) {
          setScanResult(response.data);
        } else {
          throw new Error("No scan result data received");
        }
      } catch (err) {
        if (!isMounted) return;

        console.error("Error fetching scan result:", err);
        setError(err instanceof Error ? err.message : "Failed to load scan results");
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchScanResult();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [scanId]);

  const handleGoBack = () => {
    router.push('/');
  };

  if (loading) {
    return (
      <DefaultLayout>
        <Head>
          <title>Loading Scan Results - SafePush</title>
          <meta name="description" content="Loading security scan results..." />
        </Head>
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="inline-block max-w-lg text-center justify-center">
            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <CardBody className="p-8 text-center">
                <Spinner size="lg" className="mb-4" />
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Loading Scan Results
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Please wait while we retrieve your security scan results...
                </p>
              </CardBody>
            </Card>
          </div>
        </section>
      </DefaultLayout>
    );
  }

  if (error) {
    return (
      <DefaultLayout>
        <Head>
          <title>Error Loading Scan Results - SafePush</title>
          <meta name="description" content="Error loading security scan results" />
        </Head>
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="inline-block max-w-lg text-center justify-center">
            <Card className="bg-red-50 dark:bg-red-900/20 border border-red-300 dark:border-red-500/30">
              <CardBody className="p-8 text-center">
                <ExclamationTriangleIcon className="w-12 h-12 text-red-600 dark:text-red-400 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-red-900 dark:text-red-100 mb-2">
                  Error Loading Scan Results
                </h2>
                <p className="text-red-700 dark:text-red-300 mb-4">
                  {error}
                </p>
                <Button
                  color="primary"
                  onPress={handleGoBack}
                  startContent={<ArrowLeftIcon className="w-4 h-4" />}
                >
                  Go Back to Scanner
                </Button>
              </CardBody>
            </Card>
          </div>
        </section>
      </DefaultLayout>
    );
  }

  if (!scanResult) {
    return (
      <DefaultLayout>
        <Head>
          <title>No Scan Results Found - SafePush</title>
          <meta name="description" content="No security scan results found" />
        </Head>
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="inline-block max-w-lg text-center justify-center">
            <Card className="bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-600">
              <CardBody className="p-8 text-center">
                <ExclamationTriangleIcon className="w-12 h-12 text-gray-600 dark:text-gray-400 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  No Scan Results Found
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  The requested scan results could not be found.
                </p>
                <Button
                  color="primary"
                  onPress={handleGoBack}
                  startContent={<ArrowLeftIcon className="w-4 h-4" />}
                >
                  Go Back to Scanner
                </Button>
              </CardBody>
            </Card>
          </div>
        </section>
      </DefaultLayout>
    );
  }

  const repositoryName = (repository as string) || scanResult.repositoryUrl?.replace(/^https?:\/\/github\.com\//, '') || 'Unknown Repository';

  return (
    <DefaultLayout>
      <Head>
        <title>Security Scan Results - {repositoryName} - SafePush</title>
        <meta name="description" content={`Security scan results for ${repositoryName} - ${scanResult.findings?.length || 0} issues found`} />
      </Head>
      <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
        <div className="inline-block max-w-7xl w-full text-center justify-center">
          <div className="mb-6">
            <Button
              variant="light"
              onPress={handleGoBack}
              startContent={<ArrowLeftIcon className="w-4 h-4" />}
              className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
            >
              Back to Scanner
            </Button>
          </div>
          
          <SimpleScanResultsDisplay
            result={scanResult}
            repositoryName={repositoryName}
            onClose={handleGoBack}
          />
        </div>
      </section>
    </DefaultLayout>
  );
}
