{"Logging": {"LogLevel": {"Microsoft.EntityFrameworkCore.Database.Command": "Warning", "Default": "Information", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Cors": {"AllowedOrigins": ["https://repoguard-security-scanner.netlify.app", "http://localhost:3000", "https://localhost:3000"]}, "GitHub": {"Token": "", "UserAgent": "UnsecuredAPIKeys-Scanner-1.0", "RateLimitBuffer": 100}, "DataCleanup": {"ScanRetentionDays": 7, "CleanupIntervalHours": 24, "EnableAutoCleanup": true, "MaxScanResultsPerRepository": 10}}