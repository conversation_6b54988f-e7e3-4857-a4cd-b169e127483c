@echo off
echo 🧹 Cleaning development environment...

echo 🔍 Checking for processes on port 3000...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
    echo ⚡ Terminating process %%a
    taskkill /PID %%a /F >nul 2>&1
)

echo 🗑️ Removing Next.js cache...
if exist .next rmdir /s /q .next >nul 2>&1

echo 🗑️ Cleaning node cache...
if exist node_modules\.cache rmdir /s /q node_modules\.cache >nul 2>&1

echo 🚀 Starting clean development server...
echo 📍 Server will be available at: http://localhost:3000
echo ⏱️ Initial compilation may take a moment...
echo.

npm run dev
