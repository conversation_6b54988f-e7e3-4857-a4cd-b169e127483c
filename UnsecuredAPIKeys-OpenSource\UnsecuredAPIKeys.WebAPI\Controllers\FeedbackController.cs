using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using UnsecuredAPIKeys.Data;
using UnsecuredAPIKeys.Data.Models;
using System.Text;

namespace UnsecuredAPIKeys.WebAPI.Controllers
{
    [ApiController]
    [Route("API/[controller]")]
    public class FeedbackController : ControllerBase
    {
        private readonly DBContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<FeedbackController> _logger;
        private readonly HttpClient _httpClient;

        public FeedbackController(
            DBContext context,
            IConfiguration configuration,
            ILogger<FeedbackController> logger,
            HttpClient httpClient)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
            _httpClient = httpClient;
        }

        [HttpPost]
        public async Task<IActionResult> SubmitFeedback([FromBody] FeedbackRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Subject) || string.IsNullOrEmpty(request.Message))
                {
                    return BadRequest("Subject and message are required.");
                }

                // Create feedback record
                var feedback = new Feedback
                {
                    Subject = request.Subject,
                    Category = request.Category ?? "general",
                    Message = request.Message,
                    UserEmail = request.UserEmail,
                    UserName = request.UserName,
                    UserId = request.UserId ?? string.Empty,
                    CreatedAt = DateTime.UtcNow
                };

                // Save to database
                _context.Feedback.Add(feedback);
                await _context.SaveChangesAsync();

                // Send email notification
                await SendFeedbackEmail(feedback);

                _logger.LogInformation("Feedback submitted successfully. ID: {FeedbackId}", feedback.Id);

                return Ok(new { message = "Feedback submitted successfully", feedbackId = feedback.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting feedback");
                return StatusCode(500, "An error occurred while submitting feedback.");
            }
        }

        private async Task SendFeedbackEmail(Feedback feedback)
        {
            try
            {
                var adminEmail = _configuration["Feedback:AdminEmail"] ?? "<EMAIL>";
                var fromEmail = _configuration["Resend:FromEmail"] ?? "<EMAIL>";
                var subject = $"[SafePush Feedback] {feedback.Category.ToUpper()}: {feedback.Subject}";
                var body = CreateFeedbackEmailBody(feedback);
                var resendApiKey = _configuration["Resend:ApiKey"] ?? "re_QKNGmgnF_G1jr4bkwmwcEYAYDZQps1XAo";

                if (string.IsNullOrEmpty(resendApiKey))
                {
                    _logger.LogWarning("Resend API key not configured. Skipping email notification.");
                    return;
                }

                var emailData = new
                {
                    from = fromEmail,
                    to = new[] { adminEmail },
                    subject = subject,
                    html = body
                };

                var json = System.Text.Json.JsonSerializer.Serialize(emailData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {resendApiKey}");

                var response = await _httpClient.PostAsync("https://api.resend.com/emails", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("Feedback email sent successfully to {AdminEmail}", adminEmail);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("Failed to send feedback email via Resend. Status: {Status}, Error: {Error}", response.StatusCode, errorContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send feedback email via Resend");
            }
        }

        private string CreateFeedbackEmailBody(Feedback feedback)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine("<!DOCTYPE html>");
            sb.AppendLine("<html>");
            sb.AppendLine("<head>");
            sb.AppendLine("<meta charset='utf-8'>");
            sb.AppendLine("<title>SafePush Feedback</title>");
            sb.AppendLine("<style>");
            sb.AppendLine("body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }");
            sb.AppendLine(".container { max-width: 600px; margin: 0 auto; padding: 20px; }");
            sb.AppendLine(".header { background: linear-gradient(135deg, #0ea5e9, #0891b2); color: white; padding: 20px; border-radius: 8px 8px 0 0; }");
            sb.AppendLine(".content { background: #f8fafc; padding: 20px; border-radius: 0 0 8px 8px; }");
            sb.AppendLine(".field { margin-bottom: 15px; }");
            sb.AppendLine(".label { font-weight: bold; color: #374151; }");
            sb.AppendLine(".value { background: white; padding: 10px; border-radius: 4px; border-left: 4px solid #0ea5e9; }");
            sb.AppendLine(".category-badge { display: inline-block; background: #0ea5e9; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; }");
            sb.AppendLine("</style>");
            sb.AppendLine("</head>");
            sb.AppendLine("<body>");
            sb.AppendLine("<div class='container'>");
            sb.AppendLine("<div class='header'>");
            sb.AppendLine("<h1>🔍 SafePush Feedback</h1>");
            sb.AppendLine("<p>New feedback has been submitted</p>");
            sb.AppendLine("</div>");
            sb.AppendLine("<div class='content'>");
            
            sb.AppendLine("<div class='field'>");
            sb.AppendLine("<div class='label'>Category:</div>");
            sb.AppendLine($"<div class='value'><span class='category-badge'>{feedback.Category.ToUpper()}</span></div>");
            sb.AppendLine("</div>");
            
            sb.AppendLine("<div class='field'>");
            sb.AppendLine("<div class='label'>Subject:</div>");
            sb.AppendLine($"<div class='value'>{feedback.Subject}</div>");
            sb.AppendLine("</div>");
            
            sb.AppendLine("<div class='field'>");
            sb.AppendLine("<div class='label'>Message:</div>");
            sb.AppendLine($"<div class='value'>{feedback.Message.Replace("\n", "<br>")}</div>");
            sb.AppendLine("</div>");
            
            if (!string.IsNullOrEmpty(feedback.UserEmail))
            {
                sb.AppendLine("<div class='field'>");
                sb.AppendLine("<div class='label'>User Email:</div>");
                sb.AppendLine($"<div class='value'>{feedback.UserEmail}</div>");
                sb.AppendLine("</div>");
            }
            
            if (!string.IsNullOrEmpty(feedback.UserName))
            {
                sb.AppendLine("<div class='field'>");
                sb.AppendLine("<div class='label'>User Name:</div>");
                sb.AppendLine($"<div class='value'>{feedback.UserName}</div>");
                sb.AppendLine("</div>");
            }
            
            sb.AppendLine("<div class='field'>");
            sb.AppendLine("<div class='label'>Submitted:</div>");
            sb.AppendLine($"<div class='value'>{feedback.CreatedAt:yyyy-MM-dd HH:mm:ss} UTC</div>");
            sb.AppendLine("</div>");
            
            sb.AppendLine("<div class='field'>");
            sb.AppendLine("<div class='label'>Feedback ID:</div>");
            sb.AppendLine($"<div class='value'>{feedback.Id}</div>");
            sb.AppendLine("</div>");
            
            sb.AppendLine("</div>");
            sb.AppendLine("</div>");
            sb.AppendLine("</body>");
            sb.AppendLine("</html>");
            
            return sb.ToString();
        }
    }

    public class FeedbackRequest
    {
        public string Subject { get; set; } = string.Empty;
        public string Category { get; set; } = "general";
        public string Message { get; set; } = string.Empty;
        public string? UserEmail { get; set; }
        public string? UserName { get; set; }
        public string? UserId { get; set; }
    }
} 