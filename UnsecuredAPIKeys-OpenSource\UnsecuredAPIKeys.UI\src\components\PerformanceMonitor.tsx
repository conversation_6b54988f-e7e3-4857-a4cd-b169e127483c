import { useEffect } from 'react';

interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

const PerformanceMonitor: React.FC = () => {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const metrics: PerformanceMetrics = {};

    // Measure Time to First Byte (TTFB)
    const measureTTFB = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        metrics.ttfb = navigation.responseStart - navigation.requestStart;
      }
    };

    // Measure Core Web Vitals
    const measureWebVitals = () => {
      // First Contentful Paint
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0];
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime;
      }

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            metrics.lcp = lastEntry.startTime;
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry: any) => {
              if (entry.processingStart && entry.startTime) {
                metrics.fid = entry.processingStart - entry.startTime;
              }
            });
          });
          fidObserver.observe({ entryTypes: ['first-input'] });

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0;
            const entries = list.getEntries();
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            });
            metrics.cls = clsValue;
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });

          // Clean up observers after 10 seconds
          setTimeout(() => {
            lcpObserver.disconnect();
            fidObserver.disconnect();
            clsObserver.disconnect();
            
            // Send metrics to analytics (only in production)
            if (process.env.NODE_ENV === 'production') {
              sendMetricsToAnalytics(metrics);
            } else {
              console.log('Performance Metrics:', metrics);
            }
          }, 10000);
        } catch (error) {
          console.warn('Performance monitoring not supported:', error);
        }
      }
    };

    const sendMetricsToAnalytics = (metrics: PerformanceMetrics) => {
      // Send to your analytics service
      if (window.gtag) {
        window.gtag('event', 'web_vitals', {
          custom_map: {
            metric_fcp: 'first_contentful_paint',
            metric_lcp: 'largest_contentful_paint',
            metric_fid: 'first_input_delay',
            metric_cls: 'cumulative_layout_shift',
            metric_ttfb: 'time_to_first_byte',
          },
          metric_fcp: metrics.fcp,
          metric_lcp: metrics.lcp,
          metric_fid: metrics.fid,
          metric_cls: metrics.cls,
          metric_ttfb: metrics.ttfb,
        });
      }
    };

    // Start measuring
    measureTTFB();
    
    // Wait for page load to measure other metrics
    if (document.readyState === 'complete') {
      measureWebVitals();
    } else {
      window.addEventListener('load', measureWebVitals);
    }

    return () => {
      window.removeEventListener('load', measureWebVitals);
    };
  }, []);

  return null; // This component doesn't render anything
};

export default PerformanceMonitor;
