import React, { useState, useMemo } from 'react';
import { Card, CardBody, CardHeader } from '@heroui/card';
import { But<PERSON> } from '@heroui/button';
import { Chip } from '@heroui/chip';
import { ScanResult, SecurityFinding } from '@/types';
import ShieldCheckIcon from '@heroicons/react/24/outline/ShieldCheckIcon';
import ExclamationTriangleIcon from '@heroicons/react/24/outline/ExclamationTriangleIcon';
import XCircleIcon from '@heroicons/react/24/outline/XCircleIcon';
import CheckCircleIcon from '@heroicons/react/24/outline/CheckCircleIcon';
import EyeIcon from '@heroicons/react/24/outline/EyeIcon';
import EyeSlashIcon from '@heroicons/react/24/outline/EyeSlashIcon';
import ArrowTopRightOnSquareIcon from '@heroicons/react/24/outline/ArrowTopRightOnSquareIcon';
import ArrowDownTrayIcon from '@heroicons/react/24/outline/ArrowDownTrayIcon';

interface SimpleScanResultsDisplayProps {
  result: ScanResult;
  repositoryName: string;
  onClose?: () => void;
}

export default function SimpleScanResultsDisplay({ result, repositoryName, onClose }: SimpleScanResultsDisplayProps) {
  const [visibleValues, setVisibleValues] = useState<Set<string>>(new Set());

  // Safely access findings with fallback
  const findings = useMemo(() => result?.findings || [], [result?.findings]);

  // Categorize findings by severity
  const { criticalFindings, highFindings, mediumFindings, lowFindings } = useMemo(() => {
    const normalizeSeverity = (severity: string | number): string => {
      try {
        if (typeof severity === 'number') {
          switch (severity) {
            case 0: return 'low';
            case 1: return 'medium';
            case 2: return 'high';
            case 3: return 'critical';
            default: return 'low';
          }
        }
        return severity?.toLowerCase() || 'low';
      } catch (error) {
        console.error('Error normalizing severity:', error);
        return 'low';
      }
    };

    const critical = findings.filter(f => normalizeSeverity(f?.severity) === 'critical');
    const high = findings.filter(f => normalizeSeverity(f?.severity) === 'high');
    const medium = findings.filter(f => normalizeSeverity(f?.severity) === 'medium');
    const low = findings.filter(f => normalizeSeverity(f?.severity) === 'low');

    return {
      criticalFindings: critical,
      highFindings: high,
      mediumFindings: medium,
      lowFindings: low
    };
  }, [findings]);

  // Add error boundary and null checks
  if (!result) {
    return (
      <Card className="bg-gray-50 border border-red-300 shadow-lg">
        <CardBody className="p-6 text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Scan Results</h3>
          <p className="text-gray-700">Unable to load scan results. Please try again.</p>
        </CardBody>
      </Card>
    );
  }

  const getSeverityColor = (severity: string | number) => {
    const normalizedSeverity = typeof severity === 'number' 
      ? ['low', 'medium', 'high', 'critical'][severity] || 'low'
      : severity?.toLowerCase() || 'low';
    
    switch (normalizedSeverity) {
      case 'critical': return 'danger';
      case 'high': return 'warning';
      case 'medium': return 'primary';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getTypeString = (type: string | number): string => {
    if (typeof type === 'number') {
      const typeMap: { [key: number]: string } = {
        0: 'API Key',
        1: 'Password',
        2: 'Token',
        3: 'Certificate',
        4: 'Private Key',
        5: 'Database Credential',
        6: 'Environment File',
        7: 'Configuration File',
        8: 'Secret Key',
        9: 'Access Token',
        10: 'Refresh Token',
        11: 'Session Token',
        12: 'Authentication Credential',
        13: 'Encryption Key',
        14: 'Signing Key',
        15: 'Webhook Secret',
        16: 'Connection String',
        17: 'Cloud Credential',
        18: 'SSH Key',
        19: 'TLS Certificate'
      };
      return typeMap[type] || 'Unknown';
    }
    return type || 'Unknown';
  };

  const toggleValueVisibility = (findingId: string) => {
    setVisibleValues(prev => {
      const newSet = new Set(prev);
      if (newSet.has(findingId)) {
        newSet.delete(findingId);
      } else {
        newSet.add(findingId);
      }
      return newSet;
    });
  };

  const generateGitHubLink = (finding: SecurityFinding): string => {
    if (!repositoryName || !finding.filePath) return '#';
    
    const baseUrl = `https://github.com/${repositoryName}`;
    const filePath = finding.filePath.startsWith('/') ? finding.filePath.slice(1) : finding.filePath;
    const lineNumber = finding.lineNumber || 1;
    
    return `${baseUrl}/blob/main/${filePath}#L${lineNumber}`;
  };

  const exportToJSON = () => {
    const exportData = {
      scanId: result.scanId,
      repositoryUrl: result.repositoryUrl,
      repositoryName: repositoryName,
      startedAt: result.startedAt,
      completedAt: result.completedAt,
      status: result.status,
      totalFindings: findings.length,
      severityBreakdown: {
        critical: criticalFindings.length,
        high: highFindings.length,
        medium: mediumFindings.length,
        low: lowFindings.length
      },
      findings: findings.map(finding => ({
        id: finding.id,
        type: getTypeString(finding.type),
        severity: finding.severity,
        title: finding.title,
        description: finding.description,
        fileName: finding.fileName,
        filePath: finding.filePath,
        lineNumber: finding.lineNumber,
        value: finding.value,
        context: finding.context,
        recommendation: finding.recommendation,
        scannerName: finding.scannerName,
        detectedAt: finding.detectedAt,
        metadata: finding.metadata
      })),
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const filename = `safepush-scan-results-${repositoryName.replace(/[^a-zA-Z0-9-]/g, '-')}-${timestamp}.json`;
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header with Summary */}
      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg">
                <ShieldCheckIcon className="w-6 h-6 text-teal-600 dark:text-teal-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  Security Scan Results
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {repositoryName} • {findings.length} issues found
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                size="sm"
                variant="bordered"
                onPress={exportToJSON}
                startContent={<ArrowDownTrayIcon className="w-4 h-4" />}
                className="text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"
              >
                Export JSON
              </Button>
              {onClose && (
                <Button
                  size="sm"
                  color="danger"
                  variant="light"
                  onPress={onClose}
                  startContent={<XCircleIcon className="w-4 h-4" />}
                >
                  Close
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="flex items-center gap-4">
            <Chip color="danger" variant="flat" size="sm">
              Critical: {criticalFindings.length}
            </Chip>
            <Chip color="warning" variant="flat" size="sm">
              High: {highFindings.length}
            </Chip>
            <Chip color="primary" variant="flat" size="sm">
              Medium: {mediumFindings.length}
            </Chip>
            <Chip color="success" variant="flat" size="sm">
              Low: {lowFindings.length}
            </Chip>
          </div>
        </CardBody>
      </Card>

      {/* Simple Table */}
      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
        <CardBody className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-900/50 border-b border-gray-200 dark:border-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Key Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    File Path
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Value
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    GitHub Link
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {findings.map((finding, index) => {
                  const findingId = `${finding.filePath}-${finding.lineNumber}-${index}`;
                  const isValueVisible = visibleValues.has(findingId);
                  const githubLink = generateGitHubLink(finding);

                  return (
                    <tr key={findingId} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          <Chip
                            color={getSeverityColor(finding.severity) as any}
                            size="sm"
                            variant="flat"
                            className="font-medium"
                          >
                            {getTypeString(finding.type)}
                          </Chip>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                        <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                          {finding.filePath}:{finding.lineNumber}
                        </code>
                      </td>
                      <td className="px-6 py-4 text-sm">
                        <div className="flex items-center gap-2">
                          <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs max-w-xs truncate">
                            {isValueVisible ? finding.value : '••••••••••••••••'}
                          </code>
                          <Button
                            size="sm"
                            variant="light"
                            isIconOnly
                            onPress={() => toggleValueVisibility(findingId)}
                            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                          >
                            {isValueVisible ? (
                              <EyeSlashIcon className="w-4 h-4" />
                            ) : (
                              <EyeIcon className="w-4 h-4" />
                            )}
                          </Button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Button
                          as="a"
                          href={githubLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          size="sm"
                          variant="bordered"
                          startContent={<ArrowTopRightOnSquareIcon className="w-4 h-4" />}
                          className="text-teal-600 dark:text-teal-400 border-teal-300 dark:border-teal-600 hover:bg-teal-50 dark:hover:bg-teal-900/20"
                        >
                          Go to GitHub
                        </Button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          
          {findings.length === 0 && (
            <div className="text-center py-12">
              <CheckCircleIcon className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No Security Issues Found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Great! Your repository appears to be secure.
              </p>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
