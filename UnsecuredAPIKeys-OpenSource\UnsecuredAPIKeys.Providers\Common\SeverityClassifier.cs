using UnsecuredAPIKeys.Data.Common;

namespace UnsecuredAPIKeys.Providers.Common
{
    /// <summary>
    /// Centralized severity classification system for security findings
    /// </summary>
    public static class SeverityClassifier
    {
        /// <summary>
        /// Determines the appropriate severity level for a security finding
        /// </summary>
        public static SecuritySeverity ClassifySeverity(
            string value, 
            string context, 
            string fileName, 
            SecurityVulnerabilityType vulnerabilityType)
        {
            var lowerValue = value.ToLowerInvariant();
            var lowerContext = context.ToLowerInvariant();
            var lowerFileName = fileName.ToLowerInvariant();

            // First check if this is likely a false positive - should be Low severity
            if (FalsePositiveFilter.IsLikelyFalsePositive(value, context, fileName))
                return SecuritySeverity.Low;

            // Check for obvious placeholders and examples - Low severity
            if (IsPlaceholderOrExample(value, context, fileName))
                return SecuritySeverity.Low;

            // Check for test/demo contexts - Medium severity at most
            if (IsTestOrDemoContext(context, fileName))
            {
                var contentSeverity = ClassifyByContent(value, context, fileName, vulnerabilityType);
                return (SecuritySeverity)Math.Min((int)SecuritySeverity.Medium, (int)contentSeverity);
            }

            // Classify based on content and context
            return ClassifyByContent(value, context, fileName, vulnerabilityType);
        }

        /// <summary>
        /// Classifies severity based on the actual content and context
        /// </summary>
        private static SecuritySeverity ClassifyByContent(
            string value, 
            string context, 
            string fileName, 
            SecurityVulnerabilityType vulnerabilityType)
        {
            var lowerValue = value.ToLowerInvariant();
            var lowerContext = context.ToLowerInvariant();
            var lowerFileName = fileName.ToLowerInvariant();

            // Critical severity conditions
            if (IsCriticalSeverity(value, context, fileName, vulnerabilityType))
                return SecuritySeverity.Critical;

            // High severity conditions
            if (IsHighSeverity(value, context, fileName, vulnerabilityType))
                return SecuritySeverity.High;

            // Medium severity conditions
            if (IsMediumSeverity(value, context, fileName, vulnerabilityType))
                return SecuritySeverity.Medium;

            // Default to Low for everything else
            return SecuritySeverity.Low;
        }

        /// <summary>
        /// Determines if a finding should be classified as Critical severity
        /// </summary>
        private static bool IsCriticalSeverity(string value, string context, string fileName, SecurityVulnerabilityType type)
        {
            var lowerValue = value.ToLowerInvariant();
            var lowerContext = context.ToLowerInvariant();
            var lowerFileName = fileName.ToLowerInvariant();

            // Production environment indicators
            if (IsProductionEnvironment(fileName, context))
                return true;

            // Private keys and certificates
            if (type == SecurityVulnerabilityType.PrivateKey || 
                type == SecurityVulnerabilityType.Certificate ||
                (value.Contains("BEGIN") && value.Contains("PRIVATE KEY")))
                return true;

            // Live/production API keys
            if (IsLiveProductionApiKey(value, context))
                return true;

            // Database credentials in production
            if (type == SecurityVulnerabilityType.DatabaseCredential && 
                !IsTestOrDemoContext(context, fileName))
                return true;

            // Cloud provider credentials
            if (IsCloudProviderCredential(value, context))
                return true;

            // Weak/default passwords
            if (type == SecurityVulnerabilityType.Password && IsWeakOrDefaultPassword(value))
                return true;

            return false;
        }

        /// <summary>
        /// Determines if a finding should be classified as High severity
        /// </summary>
        private static bool IsHighSeverity(string value, string context, string fileName, SecurityVulnerabilityType type)
        {
            var lowerValue = value.ToLowerInvariant();
            var lowerContext = context.ToLowerInvariant();

            // OAuth tokens and access tokens
            if (type == SecurityVulnerabilityType.AccessToken || 
                type == SecurityVulnerabilityType.RefreshToken ||
                lowerContext.Contains("oauth") || lowerContext.Contains("bearer"))
                return true;

            // API keys from major services (non-production)
            if (IsLegitimateApiKey(value) && !IsTestOrDemoContext(context, fileName))
                return true;

            // JWT tokens (real ones)
            if (value.StartsWith("eyJ") && value.Length > 50)
                return true;

            // High entropy strings that look like real secrets
            if (IsHighEntropyString(value) && value.Length > 32 && !IsTestOrDemoContext(context, fileName))
                return true;

            return false;
        }

        /// <summary>
        /// Determines if a finding should be classified as Medium severity
        /// </summary>
        private static bool IsMediumSeverity(string value, string context, string fileName, SecurityVulnerabilityType type)
        {
            var lowerValue = value.ToLowerInvariant();
            var lowerContext = context.ToLowerInvariant();

            // Test/demo credentials that could still be concerning
            if (IsTestOrDemoContext(context, fileName) && value.Length > 10)
                return true;

            // Environment variables that might contain secrets
            if (type == SecurityVulnerabilityType.EnvironmentFile && !IsPlaceholderOrExample(value, context, fileName))
                return true;

            // Medium entropy strings
            if (IsHighEntropyString(value) && value.Length > 16)
                return true;

            // Configuration files with potential secrets
            if (type == SecurityVulnerabilityType.ConfigurationFile)
                return true;

            return false;
        }

        /// <summary>
        /// Checks if the context indicates a production environment
        /// </summary>
        private static bool IsProductionEnvironment(string fileName, string context)
        {
            var lowerFileName = fileName.ToLowerInvariant();
            var lowerContext = context.ToLowerInvariant();

            var productionIndicators = new[] { "prod", "production", "live", "release", "deploy" };
            return productionIndicators.Any(indicator => 
                lowerFileName.Contains(indicator) || lowerContext.Contains(indicator));
        }

        /// <summary>
        /// Checks if the value is a live/production API key
        /// </summary>
        private static bool IsLiveProductionApiKey(string value, string context)
        {
            var lowerValue = value.ToLowerInvariant();
            var lowerContext = context.ToLowerInvariant();

            // OpenAI production keys
            if (value.StartsWith("sk-proj-") || 
                (value.StartsWith("sk-") && !lowerValue.Contains("test") && !lowerValue.Contains("demo")))
                return true;

            // AWS keys
            if (value.StartsWith("AKIA") || value.StartsWith("ASIA"))
                return true;

            // Stripe live keys
            if (value.Contains("sk_live_") || value.Contains("pk_live_"))
                return true;

            // Production context indicators
            if (lowerContext.Contains("live") || lowerContext.Contains("prod") || lowerContext.Contains("production"))
                return true;

            return false;
        }

        /// <summary>
        /// Checks if the value is a cloud provider credential
        /// </summary>
        private static bool IsCloudProviderCredential(string value, string context)
        {
            var lowerContext = context.ToLowerInvariant();
            
            var cloudIndicators = new[] { "aws", "azure", "gcp", "google cloud", "cloud", "s3", "ec2" };
            return cloudIndicators.Any(indicator => lowerContext.Contains(indicator));
        }

        /// <summary>
        /// Checks if the value is a legitimate API key from a known service
        /// </summary>
        private static bool IsLegitimateApiKey(string value)
        {
            // Known API key prefixes
            var apiKeyPrefixes = new[] { "AIza", "ghp_", "gho_", "SG.", "xox", "sk-", "pk_" };
            return apiKeyPrefixes.Any(prefix => value.StartsWith(prefix));
        }

        /// <summary>
        /// Checks if the password is weak or uses default values
        /// </summary>
        private static bool IsWeakOrDefaultPassword(string value)
        {
            var weakPasswords = new[] { "password", "123456", "admin", "root", "guest", "test", "default" };
            return weakPasswords.Any(weak => value.ToLowerInvariant().Contains(weak));
        }

        /// <summary>
        /// Checks if the value is a placeholder or example
        /// </summary>
        private static bool IsPlaceholderOrExample(string value, string context, string fileName)
        {
            var lowerValue = value.ToLowerInvariant();
            var lowerContext = context.ToLowerInvariant();
            var lowerFileName = fileName.ToLowerInvariant();

            // Placeholder indicators
            var placeholders = new[] { "placeholder", "example", "your_", "replace", "demo", "sample", "test_", "dummy" };
            if (placeholders.Any(p => lowerValue.Contains(p) || lowerContext.Contains(p)))
                return true;

            // Template files
            if (lowerFileName.Contains("template") || lowerFileName.Contains("example") || lowerFileName.Contains("sample"))
                return true;

            return false;
        }

        /// <summary>
        /// Checks if the context indicates test or demo environment
        /// </summary>
        private static bool IsTestOrDemoContext(string context, string fileName)
        {
            var lowerContext = context.ToLowerInvariant();
            var lowerFileName = fileName.ToLowerInvariant();

            var testIndicators = new[] { "test", "demo", "sample", "mock", "fixture", "spec", "dev", "development" };
            return testIndicators.Any(indicator => 
                lowerContext.Contains(indicator) || lowerFileName.Contains(indicator));
        }

        /// <summary>
        /// Checks if a string has high entropy (likely random/generated)
        /// </summary>
        private static bool IsHighEntropyString(string value)
        {
            if (string.IsNullOrEmpty(value) || value.Length < 8)
                return false;

            var uniqueChars = value.Distinct().Count();
            var entropy = (double)uniqueChars / value.Length;
            
            // Consider high entropy if more than 60% of characters are unique
            return entropy > 0.6 && uniqueChars > 8;
        }
    }
}
