{"format": 1, "restore": {"C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers.Tests\\UnsecuredAPIKeys.Providers.Tests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj", "projectName": "UnsecuredAPIKeys.Data", "projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.8, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers.Tests\\UnsecuredAPIKeys.Providers.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers.Tests\\UnsecuredAPIKeys.Providers.Tests.csproj", "projectName": "UnsecuredAPIKeys.Providers.Tests", "projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers.Tests\\UnsecuredAPIKeys.Providers.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers.Tests\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\UnsecuredAPIKeys.Providers.csproj": {"projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\UnsecuredAPIKeys.Providers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.11.1, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\UnsecuredAPIKeys.Providers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\UnsecuredAPIKeys.Providers.csproj", "projectName": "UnsecuredAPIKeys.Providers", "projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\UnsecuredAPIKeys.Providers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "NGitLab": {"target": "Package", "version": "[9.2.0, )"}, "Octokit": {"target": "Package", "version": "[14.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}