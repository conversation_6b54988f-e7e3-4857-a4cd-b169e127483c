# Clean Development Server Script
# This script ensures a clean development environment

Write-Host "🧹 Cleaning development environment..." -ForegroundColor Cyan

# Kill any processes using port 3000
Write-Host "🔍 Checking for processes on port 3000..." -ForegroundColor Yellow
$processes = netstat -ano | findstr :3000
if ($processes) {
    Write-Host "⚡ Found processes on port 3000, terminating..." -ForegroundColor Yellow
    $pids = ($processes | ForEach-Object { ($_ -split '\s+')[-1] }) | Sort-Object -Unique
    foreach ($pid in $pids) {
        if ($pid -and $pid -ne "0") {
            try {
                taskkill /PID $pid /F 2>$null
                Write-Host "✅ Terminated process $pid" -ForegroundColor Green
            } catch {
                Write-Host "⚠️  Could not terminate process $pid" -ForegroundColor Red
            }
        }
    }
    Start-Sleep -Seconds 2
} else {
    Write-Host "✅ Port 3000 is free" -ForegroundColor Green
}

# Clean Next.js cache
Write-Host "🗑️  Removing Next.js cache..." -ForegroundColor Yellow
if (Test-Path ".next") {
    Remove-Item -Recurse -Force .next -ErrorAction SilentlyContinue
    Write-Host "✅ Next.js cache cleared" -ForegroundColor Green
} else {
    Write-Host "✅ No cache to clear" -ForegroundColor Green
}

# Clean node_modules/.cache if it exists
Write-Host "🗑️  Cleaning node cache..." -ForegroundColor Yellow
if (Test-Path "node_modules\.cache") {
    Remove-Item -Recurse -Force "node_modules\.cache" -ErrorAction SilentlyContinue
    Write-Host "✅ Node cache cleared" -ForegroundColor Green
}

Write-Host "🚀 Starting clean development server..." -ForegroundColor Cyan
Write-Host "📍 Server will be available at: http://localhost:3000" -ForegroundColor Magenta
Write-Host "⏱️  Initial compilation may take a moment..." -ForegroundColor Yellow
Write-Host "" 

# Start the development server
npm run dev
