using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace UnsecuredAPIKeys.Data.Models
{
    [Table("Feedback")]
    public class Feedback
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        [Required]
        [MaxLength(100)]
        public string Subject { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(50)]
        public string Category { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(2000)]
        public string Message { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string? UserEmail { get; set; }
        
        [MaxLength(100)]
        public string? UserName { get; set; }
        
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        [Required]
        public string UserId { get; set; } = string.Empty; // Can be empty for anonymous feedback
        
        [Required]
        public bool IsProcessed { get; set; } = false;
        
        [MaxLength(500)]
        public string? AdminResponse { get; set; }
        
        public DateTime? RespondedAt { get; set; }
    }
} 