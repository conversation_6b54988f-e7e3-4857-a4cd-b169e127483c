using System.Text.RegularExpressions;

namespace UnsecuredAPIKeys.Providers.Common
{
    /// <summary>
    /// Comprehensive false positive filtering system for security scanners
    /// </summary>
    public static class FalsePositiveFilter
    {
        /// <summary>
        /// Common character sets used for random string generation
        /// </summary>
        private static readonly HashSet<string> CommonCharacterSets = new(StringComparer.OrdinalIgnoreCase)
        {
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********",
            "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
            "abcdefghijklmnopqrstuvwxyz",
            "**********",
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
            "**********ABCDEFGHIJKLMNOPQRSTUVWXYZ",
            "**********abcdefghijklmnopqrstuvwxyz",
            "!@#$%^&*()_+-=[]{}|;:,.<>?",
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********!@#$%^&*"
        };

        /// <summary>
        /// Common placeholder patterns that should not be flagged
        /// </summary>
        private static readonly string[] PlaceholderPatterns = new[]
        {
            "your_", "change_me", "replace_", "example_", "placeholder", "sample_", "demo_",
            "dummy", "test_", "xxx", "yyy", "zzz", "12345", "abcde", "password", "secret",
            "key_here", "api_key_here", "sk-proj-1234", "sk-1234", "your_api_key",
            "insert_your", "replace_with", "add_your", "put_your", "enter_your",
            "your-secret-key-here", "your_google_client_secret", "google_oauth_client_id",
            "your_project_id", "your_openai_api_key", "your_api_key_here", "client_secret_here",
            "secret_key_here", "token_here", "key_placeholder", "api_placeholder"
        };

        /// <summary>
        /// Regex patterns that indicate legitimate code patterns
        /// </summary>
        private static readonly string[] RegexIndicators = new[]
        {
            @"\(\[\^", @"\(\?\:", @"\\s\*", @"\\d", @"\\w", @"\$\)", @"\\-", @"\\|", 
            @"–", @"\\+", @"\\\?", @"\.\*", @"\.\+", @"\[\^", @"\]\+", @"\]\*", @"\]\?"
        };

        /// <summary>
        /// Context keywords that suggest legitimate code patterns
        /// </summary>
        private static readonly Dictionary<string, string[]> ContextKeywords = new()
        {
            ["character_sets"] = new[] { "characters", "charset", "alphabet", "random", "generate", "string", "const characters", "var characters", "let characters" },
            ["regex_patterns"] = new[] { "regex", "regexp", "pattern", "match", "r'", "r\"", "re.", "Pattern.", "Regex." },
            ["configuration"] = new[] { "config", "settings", "default", "fallback", "template", "example", "sample", "credentials", "oauth" },
            ["documentation"] = new[] { "example", "sample", "demo", "tutorial", "readme", "documentation", "docs", "guide" },
            ["testing"] = new[] { "test", "spec", "mock", "fixture", "stub", "fake", "dummy", "unittest", "jest", "mocha" },
            ["comments"] = new[] { "//", "#", "/*", "*/", "<!--", "-->", "\"\"\"", "'''" },
            ["environment_vars"] = new[] { "os.environ.get", "process.env", "getenv", "env.", "environment", "environ", "env_var" },
            ["base64_operations"] = new[] { "base64", "b64decode", "b64encode", "urlsafe_b64decode", "decode('utf-8')", "encode('utf-8')" }
        };

        /// <summary>
        /// File patterns that commonly contain false positives
        /// </summary>
        private static readonly string[] FalsePositiveFilePatterns = new[]
        {
            "package-lock.json", "yarn.lock", "composer.lock", "pipfile.lock", "gemfile.lock",
            "cargo.lock", "go.sum", "pnpm-lock.yaml", "poetry.lock", "requirements.txt",
            "node_modules/", ".git/", "vendor/", "dist/", "build/", "target/", "bin/", "obj/",
            ".env.example", "config.example", "template", "sample", "demo"
        };

        /// <summary>
        /// Main method to check if a match is likely a false positive
        /// </summary>
        public static bool IsLikelyFalsePositive(string match, string context, string fileName)
        {
            if (string.IsNullOrWhiteSpace(match) || string.IsNullOrWhiteSpace(context))
                return false;

            var cleanMatch = match.Trim('"', '\'', ' ', '\t', '\n', '\r');
            var lowerContext = context.ToLowerInvariant();
            var lowerFileName = fileName.ToLowerInvariant();

            // Check for common character sets
            if (IsCommonCharacterSet(cleanMatch, lowerContext))
                return true;

            // Check for placeholder patterns
            if (IsPlaceholderPattern(cleanMatch, lowerContext))
                return true;

            // Check for regex patterns
            if (IsRegexPattern(cleanMatch, lowerContext))
                return true;

            // Check for configuration templates
            if (IsConfigurationTemplate(cleanMatch, lowerContext, lowerFileName))
                return true;

            // Check for documentation examples
            if (IsDocumentationExample(cleanMatch, lowerContext, lowerFileName))
                return true;

            // Check for test/mock data
            if (IsTestOrMockData(cleanMatch, lowerContext, lowerFileName))
                return true;

            // Check for dependency/generated files
            if (IsDependencyOrGeneratedFile(lowerFileName))
                return true;

            // Check for environment variable references
            if (IsEnvironmentVariableReference(cleanMatch, lowerContext))
                return true;

            // Check for base64 operations
            if (IsBase64Operation(cleanMatch, lowerContext))
                return true;

            return false;
        }

        /// <summary>
        /// Checks if the match is a common character set used for random generation
        /// </summary>
        private static bool IsCommonCharacterSet(string match, string lowerContext)
        {
            // Direct match against known character sets
            if (CommonCharacterSets.Contains(match))
                return true;

            // Check for partial matches of character sets
            foreach (var charset in CommonCharacterSets)
            {
                if (match.Length >= 20 && charset.Contains(match))
                    return true;
            }

            // Check context for character set indicators
            var charsetIndicators = ContextKeywords["character_sets"];
            if (charsetIndicators.Any(indicator => lowerContext.Contains(indicator)))
            {
                // Additional validation for sequential patterns
                if (IsSequentialPattern(match))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Checks if the match contains sequential patterns (like alphabets or numbers)
        /// </summary>
        private static bool IsSequentialPattern(string match)
        {
            if (match.Length < 10) return false;

            // Check for sequential alphabets
            if (match.Contains("ABCDEFGHIJKLMNOPQRSTUVWXYZ") || 
                match.Contains("abcdefghijklmnopqrstuvwxyz") ||
                match.Contains("**********"))
                return true;

            // Check for partial sequential patterns
            var sequences = new[] { "ABCDEFGHIJKLM", "abcdefghijklm", "**********" };
            return sequences.Any(seq => match.Contains(seq));
        }

        /// <summary>
        /// Checks if the match is a placeholder pattern
        /// </summary>
        private static bool IsPlaceholderPattern(string match, string lowerContext)
        {
            var lowerMatch = match.ToLowerInvariant();
            
            // Direct placeholder check
            if (PlaceholderPatterns.Any(placeholder => lowerMatch.Contains(placeholder)))
                return true;

            // Context-based placeholder detection
            if (lowerContext.Contains("placeholder") || lowerContext.Contains("example") ||
                lowerContext.Contains("replace") || lowerContext.Contains("your_"))
                return true;

            return false;
        }

        /// <summary>
        /// Checks if the match is part of a regex pattern
        /// </summary>
        private static bool IsRegexPattern(string match, string lowerContext)
        {
            // Check context for regex indicators
            var regexIndicators = ContextKeywords["regex_patterns"];
            if (regexIndicators.Any(indicator => lowerContext.Contains(indicator)))
                return true;

            // Check for regex-specific syntax in the match
            if (RegexIndicators.Any(indicator => match.Contains(indicator)))
                return true;

            return false;
        }

        /// <summary>
        /// Checks if the match is in a configuration template
        /// </summary>
        private static bool IsConfigurationTemplate(string match, string lowerContext, string lowerFileName)
        {
            // Check file name patterns
            if (FalsePositiveFilePatterns.Any(pattern => lowerFileName.Contains(pattern)))
                return true;

            // Check context indicators
            var configIndicators = ContextKeywords["configuration"];
            return configIndicators.Any(indicator => lowerContext.Contains(indicator));
        }

        /// <summary>
        /// Checks if the match is in documentation
        /// </summary>
        private static bool IsDocumentationExample(string match, string lowerContext, string lowerFileName)
        {
            // Check for documentation file patterns
            if (lowerFileName.Contains("readme") || lowerFileName.Contains("doc") ||
                lowerFileName.Contains("guide") || lowerFileName.Contains("tutorial"))
                return true;

            // Check context indicators
            var docIndicators = ContextKeywords["documentation"];
            return docIndicators.Any(indicator => lowerContext.Contains(indicator));
        }

        /// <summary>
        /// Checks if the match is test or mock data
        /// </summary>
        private static bool IsTestOrMockData(string match, string lowerContext, string lowerFileName)
        {
            // Check file name patterns
            if (lowerFileName.Contains("test") || lowerFileName.Contains("spec") ||
                lowerFileName.Contains("mock") || lowerFileName.Contains("fixture"))
                return true;

            // Check context indicators
            var testIndicators = ContextKeywords["testing"];
            return testIndicators.Any(indicator => lowerContext.Contains(indicator));
        }

        /// <summary>
        /// Checks if the file is a dependency or generated file
        /// </summary>
        private static bool IsDependencyOrGeneratedFile(string lowerFileName)
        {
            return FalsePositiveFilePatterns.Any(pattern => lowerFileName.Contains(pattern));
        }

        /// <summary>
        /// Checks if the match is an environment variable reference
        /// </summary>
        private static bool IsEnvironmentVariableReference(string match, string lowerContext)
        {
            // Check for environment variable access patterns
            var envIndicators = ContextKeywords["environment_vars"];
            if (envIndicators.Any(indicator => lowerContext.Contains(indicator)))
                return true;

            // Check for common environment variable naming patterns
            var envVarPatterns = new[] { "_key", "_secret", "_token", "_api", "_id", "_url" };
            if (envVarPatterns.Any(pattern => match.ToLowerInvariant().EndsWith(pattern)))
            {
                // Additional validation - check if it's in an environment context
                if (lowerContext.Contains("environ") || lowerContext.Contains("env") ||
                    lowerContext.Contains("getenv") || lowerContext.Contains("process.env"))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Checks if the match is part of base64 encoding/decoding operations
        /// </summary>
        private static bool IsBase64Operation(string match, string lowerContext)
        {
            // Check for base64 operation indicators
            var base64Indicators = ContextKeywords["base64_operations"];
            if (base64Indicators.Any(indicator => lowerContext.Contains(indicator)))
                return true;

            // Check if the match looks like base64 data in a decoding context
            if (match.Length > 20 && IsBase64String(match))
            {
                if (lowerContext.Contains("decode") || lowerContext.Contains("b64") ||
                    lowerContext.Contains("base64") || lowerContext.Contains("payload"))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Checks if a string looks like base64 encoded data
        /// </summary>
        private static bool IsBase64String(string value)
        {
            if (string.IsNullOrEmpty(value)) return false;

            // Base64 strings only contain A-Z, a-z, 0-9, +, /, and = for padding
            var base64Pattern = @"^[A-Za-z0-9+/]*={0,2}$";
            return System.Text.RegularExpressions.Regex.IsMatch(value, base64Pattern);
        }
    }
}
