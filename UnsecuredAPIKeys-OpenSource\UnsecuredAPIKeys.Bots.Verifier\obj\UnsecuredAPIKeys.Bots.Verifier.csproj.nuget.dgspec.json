{"format": 1, "restore": {"C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Bots.Verifier\\UnsecuredAPIKeys.Bots.Verifier.csproj": {}}, "projects": {"C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Bots.Verifier\\UnsecuredAPIKeys.Bots.Verifier.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Bots.Verifier\\UnsecuredAPIKeys.Bots.Verifier.csproj", "projectName": "UnsecuredAPIKeys.Bots.Verifier", "projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Bots.Verifier\\UnsecuredAPIKeys.Bots.Verifier.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Bots.Verifier\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj"}, "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\UnsecuredAPIKeys.Providers.csproj": {"projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\UnsecuredAPIKeys.Providers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"LangChain": {"target": "Package", "version": "[0.17.0, )"}, "LangChain.Providers.Anthropic": {"target": "Package", "version": "[0.17.0, )"}, "LangChain.Providers.Google": {"target": "Package", "version": "[0.17.0, )"}, "LangChain.Providers.OpenAI": {"target": "Package", "version": "[0.17.0, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.22.1, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj", "projectName": "UnsecuredAPIKeys.Data", "projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.8, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\UnsecuredAPIKeys.Providers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\UnsecuredAPIKeys.Providers.csproj", "projectName": "UnsecuredAPIKeys.Providers", "projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\UnsecuredAPIKeys.Providers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Providers\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\gitscrappper\\UnsecuredAPIKeys-OpenSource\\UnsecuredAPIKeys.Data\\UnsecuredAPIKeys.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "NGitLab": {"target": "Package", "version": "[9.2.0, )"}, "Octokit": {"target": "Package", "version": "[14.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}