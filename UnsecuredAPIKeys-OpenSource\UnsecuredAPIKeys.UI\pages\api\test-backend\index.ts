import type { NextApiRequest, NextApiResponse } from 'next';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://repoguard-backend.onrender.com';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('Testing backend connection to:', API_BASE_URL);
    
    // Test basic connectivity
    const response = await fetch(`${API_BASE_URL}/API/GetDisplayCount`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Backend response status:', response.status);
    console.log('Backend response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      return res.status(200).json({
        success: false,
        backend_url: API_BASE_URL,
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
    }

    const data = await response.text();
    return res.status(200).json({
      success: true,
      backend_url: API_BASE_URL,
      status: response.status,
      data: data
    });

  } catch (error) {
    console.error('Backend test error:', error);
    return res.status(200).json({
      success: false,
      backend_url: API_BASE_URL,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
