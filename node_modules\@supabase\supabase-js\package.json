{"name": "@supabase/supabase-js", "version": "2.53.0", "description": "Isomorphic Javascript client for Supabase", "keywords": ["javascript", "typescript", "supabase"], "homepage": "https://github.com/supabase/supabase-js", "bugs": "https://github.com/supabase/supabase-js/issues", "license": "MIT", "author": "Supabase", "files": ["dist", "src"], "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "sideEffects": false, "repository": "supabase/supabase-js", "scripts": {"clean": "rimraf dist docs/v2", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "run-s clean format build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "build:umd": "webpack --env mode=production", "types-generate": "dts-gen -m '@supabase/supabase-js' -s", "test": "run-s test:types test:run", "test:all": "run-s test:types test:run test:integration test:integration:browser", "test:run": "jest --runInBand --detect<PERSON><PERSON>Handles", "test:unit": "jest --runInBand --detectOpenHandles test/unit", "test:coverage": "jest --runInBand --coverage --testPathIgnorePatterns=\"test/integration|test/deno\"", "test:integration": "jest --runInBand --detectOpenHandles test/integration.test.ts", "test:integration:browser": "deno test --allow-all test/integration.browser.test.ts", "test:edge-functions": "deno test --allow-all --no-check test/deno/edge-functions-integration.test.ts", "test:watch": "jest --watch --verbose false --silent false", "test:node:playwright": "cd test/integration/node-browser && npm install && cp ../../../dist/umd/supabase.js . && npm run test", "test:bun": "cd test/integration/bun && bun install && bun test", "test:types": "run-s build:module && tsd --files test/types/*.test-d.ts && jsr publish --dry-run", "docs": "typedoc --entryPoints src/index.ts --out docs/v2 --includes src/**/*.ts", "docs:json": "typedoc --entryPoints src/index.ts --includes src/**/*.ts --json docs/v2/spec.json --excludeExternals", "serve:coverage": "npm run test:coverage && serve test/coverage", "update:test-deps": "npm run build && npm pack && cp supabase-supabase-js-*.tgz test/integration/expo/supabase-supabase-js-0.0.0-automated.tgz && cp supabase-supabase-js-*.tgz test/integration/next/supabase-supabase-js-0.0.0-automated.tgz && cp supabase-supabase-js-*.tgz test/deno/supabase-supabase-js-0.0.0-automated.tgz && cp supabase-supabase-js-*.tgz test/integration/bun/supabase-supabase-js-0.0.0-automated.tgz && cd test/integration/expo && npm install && cd ../next && npm install --legacy-peer-deps && cd ../../deno && npm install && cd ../integration/bun && bun install", "update:test-deps:expo": "npm run build && npm pack && cp supabase-supabase-js-*.tgz test/integration/expo/supabase-supabase-js-0.0.0-automated.tgz && cd test/integration/expo && npm install", "update:test-deps:next": "npm run build && npm pack && cp supabase-supabase-js-*.tgz test/integration/next/supabase-supabase-js-0.0.0-automated.tgz && cd test/integration/next && npm install --legacy-peer-deps", "update:test-deps:deno": "npm run build && npm pack && cp supabase-supabase-js-*.tgz test/deno/supabase-supabase-js-0.0.0-automated.tgz && cd test/deno && npm install", "update:test-deps:bun": "npm run build && npm pack && cp supabase-supabase-js-*.tgz test/integration/bun/supabase-supabase-js-0.0.0-automated.tgz && cd test/integration/bun && bun install"}, "dependencies": {"@supabase/auth-js": "2.71.1", "@supabase/functions-js": "2.4.5", "@supabase/node-fetch": "2.6.15", "@supabase/postgrest-js": "1.19.4", "@supabase/realtime-js": "2.11.15", "@supabase/storage-js": "^2.10.4"}, "devDependencies": {"@sebbo2002/semantic-release-jsr": "^1.0.0", "@solana/wallet-standard-features": "^1.3.0", "@types/jest": "^29.2.5", "husky": "^4.3.8", "jest": "^29.3.1", "jsr": "^0.13.5", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "pretty-quick": "^3.1.3", "puppeteer": "^24.9.0", "rimraf": "^3.0.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "serve": "^14.2.1", "ts-jest": "^29.0.5", "ts-loader": "^8.0.11", "ts-node": "^10.9.1", "tsd": "^0.30.4", "typedoc": "^0.22.16", "typescript": "^4.5.5", "webpack": "^5.69.1", "webpack-cli": "^4.9.2"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "jsdelivr": "dist/umd/supabase.js", "unpkg": "dist/umd/supabase.js", "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}