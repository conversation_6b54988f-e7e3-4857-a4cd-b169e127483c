'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { 
  ShieldCheckIcon, 
  RocketLaunchIcon,
  CheckCircleIcon,
  CodeBracketIcon,
  LockClosedIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import { GithubIcon } from './icons'
import { useAuth } from '@/contexts/AuthContext'

export default function GitHubOnlyAuthComponent() {
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' | 'info' } | null>(null)

  const { signInWithGitHub } = useAuth()

  const handleGitHubAuth = async () => {
    setLoading(true)
    setMessage(null)

    try {
      const { error } = await signInWithGitHub()
      if (error) {
        setMessage({ text: error.message, type: 'error' })
      }
    } catch (error) {
      setMessage({ text: 'An unexpected error occurred', type: 'error' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-gray-900 via-teal-900/20 to-gray-900">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-teal-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-500/8 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-teal-500/5 via-transparent to-transparent rounded-full" />
      </div>

      {/* Main Auth Container */}
      <div className="w-full max-w-lg relative z-10">
        {/* Modern Card Design */}
        <div className="bg-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-3xl shadow-2xl shadow-teal-500/10">
          <div className="p-10">
            {/* Logo and Brand */}
            <div className="text-center mb-10">
              <div className="w-20 h-20 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-xl shadow-teal-500/25">
                <ShieldCheckIcon className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-white mb-3">
                SafePush
              </h1>
              <p className="text-gray-400 text-lg">
                Secure Better. Push. Track. Dominate
              </p>
            </div>

            {/* Welcome Message */}
            <div className="text-center mb-10">
              <h2 className="text-2xl font-semibold text-white mb-3">
                Welcome to SafePush
              </h2>
              <p className="text-gray-400">
                Professional security scanning for your GitHub repositories
              </p>
            </div>

            {/* GitHub OAuth Button */}
            <div className="space-y-6">
              <button
                type="button"
                onClick={handleGitHubAuth}
                disabled={loading}
                className="w-full h-14 bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white rounded-2xl flex items-center justify-center gap-4 text-base font-semibold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none border border-gray-600/50 shadow-xl shadow-gray-900/20"
              >
                <GithubIcon className="w-6 h-6" />
                <span>Continue with GitHub</span>
                {loading && <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2" />}
              </button>

              {/* Message Display */}
              {message && (
                <div className={`p-4 rounded-xl text-sm border ${
                  message.type === 'success'
                    ? 'bg-green-500/10 text-green-400 border-green-500/20'
                    : message.type === 'error'
                    ? 'bg-red-500/10 text-red-400 border-red-500/20'
                    : 'bg-blue-500/10 text-blue-400 border-blue-500/20'
                }`}>
                  {message.text}
                </div>
              )}
            </div>

            {/* Features Preview */}
            <div className="mt-10 space-y-4">
              <h3 className="text-lg font-semibold text-white text-center mb-6">
                What you'll get access to:
              </h3>
              
              <div className="grid gap-4">
                <div className="flex items-center gap-3 p-3 bg-gray-700/30 rounded-xl border border-gray-600/30">
                  <div className="w-8 h-8 bg-teal-500/20 rounded-lg flex items-center justify-center">
                    <ShieldCheckIcon className="w-4 h-4 text-teal-400" />
                  </div>
                  <div>
                    <p className="text-white text-sm font-medium">Security Scanning</p>
                    <p className="text-gray-400 text-xs">Detect API keys and vulnerabilities</p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-gray-700/30 rounded-xl border border-gray-600/30">
                  <div className="w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center">
                    <CodeBracketIcon className="w-4 h-4 text-cyan-400" />
                  </div>
                  <div>
                    <p className="text-white text-sm font-medium">Repository Analysis</p>
                    <p className="text-gray-400 text-xs">Deep code analysis and reporting</p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-gray-700/30 rounded-xl border border-gray-600/30">
                  <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <RocketLaunchIcon className="w-4 h-4 text-purple-400" />
                  </div>
                  <div>
                    <p className="text-white text-sm font-medium">Dashboard & Analytics</p>
                    <p className="text-gray-400 text-xs">Track security improvements</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Privacy Notice */}
            <div className="text-center pt-8 border-t border-gray-700/50 mt-10">
              <div className="flex items-center justify-center gap-2 text-xs text-gray-500 mb-2">
                <LockClosedIcon className="w-4 h-4 text-teal-400" />
                <span>We are not storing anything - Your privacy is our priority</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
                <CheckCircleIcon className="w-4 h-4 text-green-400" />
                <span>Secured with enterprise-grade encryption</span>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="text-center mt-8">
          <p className="text-gray-500 text-sm">
            By continuing, you agree to our{' '}
            <Link href="/terms" className="text-teal-400 hover:text-teal-300 transition-colors">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="/privacy" className="text-teal-400 hover:text-teal-300 transition-colors">
              Privacy Policy
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
