import React from "react";
import Head from "next/head";
import DefaultLayout from "@/layouts/default";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Chip } from "@heroui/chip";

export default function PrivacyPage() {
  return (
    <>
      <Head>
        <title>Privacy Policy - SafePush</title>
        <meta name="description" content="SafePush Privacy Policy - We are not storing anything. Your privacy is our priority." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <DefaultLayout>
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="max-w-4xl mx-auto px-6">
            <div className="text-center mb-12">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                Privacy Policy
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Your privacy and security are our top priorities
              </p>
            </div>

            <Card className="mb-8 border-2 border-teal-200 dark:border-teal-800">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">🔒</span>
                  <div>
                    <h3 className="text-xl font-bold text-teal-600 dark:text-teal-400">
                      We Are Not Storing Anything
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Your privacy and security are our top priorities
                    </p>
                  </div>
                  <Chip color="success" variant="flat" size="sm">
                    Zero Storage
                  </Chip>
                </div>
              </CardHeader>
              
              <CardBody className="pt-0">
                <div className="grid md:grid-cols-2 gap-6">
                  {/* What We Do */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-green-600 dark:text-green-400 flex items-center gap-2">
                      ✅ What We Do
                    </h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start gap-2">
                        <span className="text-green-500 mt-1">•</span>
                        <span>Scan only public repositories or repositories you have explicit access to</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-green-500 mt-1">•</span>
                        <span>Process scan results in real-time without permanent storage</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-green-500 mt-1">•</span>
                        <span>Use secure, encrypted connections for all communications</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-green-500 mt-1">•</span>
                        <span>Respect your GitHub permissions and access controls</span>
                      </li>
                    </ul>
                  </div>

                  {/* What We Don't Do */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-red-600 dark:text-red-400 flex items-center gap-2">
                      ❌ What We Don't Do
                    </h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start gap-2">
                        <span className="text-red-500 mt-1">•</span>
                        <span>Store your source code or file contents</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-red-500 mt-1">•</span>
                        <span>Keep scan results in our database</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-red-500 mt-1">•</span>
                        <span>Share your data with third parties</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-red-500 mt-1">•</span>
                        <span>Track your personal information</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-red-500 mt-1">•</span>
                        <span>Use cookies for tracking</span>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Security Measures */}
                <div className="mt-6 p-4 bg-teal-50 dark:bg-teal-950/30 rounded-lg border border-teal-200 dark:border-teal-800">
                  <h4 className="font-semibold text-teal-600 dark:text-teal-400 mb-2 flex items-center gap-2">
                    🛡️ Security Measures
                  </h4>
                  <div className="text-sm space-y-2">
                    <p>
                      <strong>Encryption:</strong> All data is encrypted in transit using industry-standard TLS
                    </p>
                    <p>
                      <strong>Access Control:</strong> We only access repositories you explicitly grant permission to
                    </p>
                    <p>
                      <strong>No Persistence:</strong> Scan results are processed and displayed without storage
                    </p>
                    <p>
                      <strong>Open Source:</strong> Our scanning methods are transparent and auditable
                    </p>
                  </div>
                </div>

                {/* Contact */}
                <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-900/30 rounded-lg border border-gray-200 dark:border-gray-800">
                  <h4 className="font-semibold text-gray-600 dark:text-gray-400 mb-2 flex items-center gap-2">
                    📧 Questions About Privacy?
                  </h4>
                  <p className="text-sm">
                    If you have any questions about our privacy practices, please contact us through our feedback form.
                    We're committed to transparency and will be happy to address any concerns.
                  </p>
                </div>
              </CardBody>
            </Card>

            <div className="text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Last updated: {new Date().toLocaleDateString()}
              </p>
            </div>
          </div>
        </section>
      </DefaultLayout>
    </>
  );
}
