import type { NextApiRequest, NextApiResponse } from 'next';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://repoguard-backend.onrender.com';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Add CORS headers for development
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { subject, category, message, userEmail, userName, userId } = req.body;

    // Validate required fields
    if (!subject || !message) {
      return res.status(400).json({ error: 'Subject and message are required' });
    }

    console.log('Feedback API: Received request', { subject, category, userEmail, userName });

    // Forward the request to the backend API
    console.log('Feedback API: Forwarding to backend:', `${API_BASE_URL}/API/Feedback`);

    const response = await fetch(`${API_BASE_URL}/API/Feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subject,
        category: category || 'general',
        message,
        userEmail,
        userName,
        userId
      }),
    }).catch(fetchError => {
      console.error('Fetch error:', fetchError);
      throw new Error(`Network error: ${fetchError.message}`);
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend API error:', {
        status: response.status,
        statusText: response.statusText,
        url: `${API_BASE_URL}/API/Feedback`,
        error: errorText
      });
      return res.status(response.status).json({
        error: 'Failed to submit feedback',
        details: errorText,
        status: response.status,
        statusText: response.statusText
      });
    }

    const result = await response.json();
    console.log('Feedback API: Success');
    return res.status(200).json(result);

  } catch (error) {
    console.error('Error submitting feedback:', error);

    // If it's a network error, provide a helpful message
    if (error instanceof Error && error.message.includes('Network error')) {
      return res.status(503).json({
        error: 'Backend service unavailable',
        details: 'The feedback service is temporarily unavailable. Please try again later.',
        technical_details: error.message
      });
    }

    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
