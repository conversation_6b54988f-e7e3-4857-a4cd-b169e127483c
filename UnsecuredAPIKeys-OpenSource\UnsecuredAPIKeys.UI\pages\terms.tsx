import React from "react";
import Head from "next/head";
import DefaultLayout from "@/layouts/default";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Chip } from "@heroui/chip";

export default function TermsPage() {
  return (
    <>
      <Head>
        <title>Terms of Service - SafePush</title>
        <meta name="description" content="SafePush Terms of Service - Professional security scanning for developers." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <DefaultLayout>
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="max-w-4xl mx-auto px-6">
            <div className="text-center mb-12">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                Terms of Service
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Professional security scanning terms and conditions
              </p>
            </div>

            <div className="space-y-8">
              <Card className="border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    1. Service Description
                  </h3>
                </CardHeader>
                <CardBody>
                  <p className="text-gray-600 dark:text-gray-300">
                    SafePush provides automated security scanning services for GitHub repositories. 
                    Our service helps developers identify potential security vulnerabilities, exposed API keys, 
                    and other sensitive information in their code repositories.
                  </p>
                </CardBody>
              </Card>

              <Card className="border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    2. Acceptable Use
                  </h3>
                </CardHeader>
                <CardBody>
                  <div className="space-y-3 text-gray-600 dark:text-gray-300">
                    <p>You agree to use SafePush only for legitimate security scanning purposes. You may not:</p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>Scan repositories you don't have permission to access</li>
                      <li>Use the service to harm or exploit others</li>
                      <li>Attempt to reverse engineer or compromise our systems</li>
                      <li>Use the service for any illegal activities</li>
                    </ul>
                  </div>
                </CardBody>
              </Card>

              <Card className="border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    3. Data and Privacy
                  </h3>
                </CardHeader>
                <CardBody>
                  <div className="space-y-3 text-gray-600 dark:text-gray-300">
                    <p>
                      <strong>We are not storing anything.</strong> SafePush processes your repository data 
                      in real-time without permanent storage. We respect your privacy and GitHub permissions.
                    </p>
                    <p>
                      All scanning is performed with your explicit consent and within the bounds of your 
                      GitHub access permissions.
                    </p>
                  </div>
                </CardBody>
              </Card>

              <Card className="border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    4. Service Availability
                  </h3>
                </CardHeader>
                <CardBody>
                  <div className="space-y-3 text-gray-600 dark:text-gray-300">
                    <p>
                      SafePush is currently in early access. We strive to provide reliable service but 
                      cannot guarantee 100% uptime. The service is provided "as is" without warranties.
                    </p>
                    <p>
                      We reserve the right to modify, suspend, or discontinue the service at any time 
                      with reasonable notice.
                    </p>
                  </div>
                </CardBody>
              </Card>

              <Card className="border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    5. Limitation of Liability
                  </h3>
                </CardHeader>
                <CardBody>
                  <div className="space-y-3 text-gray-600 dark:text-gray-300">
                    <p>
                      SafePush is a security scanning tool designed to help identify potential issues. 
                      However, we cannot guarantee that all security vulnerabilities will be detected.
                    </p>
                    <p>
                      You are responsible for reviewing scan results and taking appropriate action. 
                      SafePush is not liable for any damages resulting from undetected vulnerabilities 
                      or false positives.
                    </p>
                  </div>
                </CardBody>
              </Card>

              <Card className="border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    6. Pricing and Changes
                  </h3>
                </CardHeader>
                <CardBody>
                  <div className="space-y-3 text-gray-600 dark:text-gray-300">
                    <p>
                      SafePush is currently free for early access users. We reserve the right to 
                      introduce pricing in the future with reasonable notice to existing users.
                    </p>
                    <p>
                      Any changes to these terms will be communicated through our platform and 
                      will take effect 30 days after notification.
                    </p>
                  </div>
                </CardBody>
              </Card>

              <Card className="border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    7. Contact Information
                  </h3>
                </CardHeader>
                <CardBody>
                  <p className="text-gray-600 dark:text-gray-300">
                    If you have questions about these terms, please contact us through our feedback form. 
                    We're committed to addressing any concerns promptly and transparently.
                  </p>
                </CardBody>
              </Card>
            </div>

            <div className="text-center mt-12">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Last updated: {new Date().toLocaleDateString()}
              </p>
            </div>
          </div>
        </section>
      </DefaultLayout>
    </>
  );
}
